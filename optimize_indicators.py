from flask import Flask
import numpy as np
import time
import optuna
import gc
import time
import traceback
import random
from datetime import datetime, timedelta, timezone
from concurrent.futures import ProcessPoolExecutor, as_completed
from sklearn.metrics import mean_squared_error
import tensorflow as tf
from common.mongo_utils import MongoUtils
from common.grafana_utils import GrafanaUtils
from common.indicators_utils import IndicatorsUtils
from common.models_utils import ModelsUtils
from common.performance_optimizer import PerformanceOptimizer, apply_performance_optimizations


SERVICE = "optimize_indicators"
logger = GrafanaUtils(service=SERVICE)
mongo = MongoUtils()
mongo.connect(service=SERVICE)
indicator = IndicatorsUtils(service=SERVICE)
models = ModelsUtils(service=SERVICE)

app = Flask(__name__)


def get_adaptive_trials(horizon_hour, optimization_type="harsh", data_size=None):
    """
    Calcule le nombre optimal de trials selon l'horizon et la complexité.

    Args:
        horizon_hour: Horizon de prédiction en heures
        optimization_type: Type d'optimisation ('harsh', 'sharp', 'ablation', 'individual', 'grouped')
        data_size: Taille du dataset (optionnel)

    Returns:
        int: Nombre optimal de trials
    """
    # Facteur de base selon l'horizon
    if horizon_hour <= 3:      # Court terme - convergence rapide
        horizon_factor = 0.7
    elif horizon_hour <= 12:   # Moyen terme - équilibré
        horizon_factor = 1.0
    else:                      # Long terme - plus de complexité
        horizon_factor = 1.3

    # Facteur selon la taille des données
    data_factor = 1.0
    if data_size:
        if data_size < 1000:
            data_factor = 0.8  # Moins de trials pour petits datasets
        elif data_size > 5000:
            data_factor = 1.2  # Plus de trials pour gros datasets

    # Nombre de base selon le type d'optimisation
    base_trials = {
        "harsh": 180,      # Réduit de 200 à 180
        "sharp": 80,       # Réduit de 100 à 80
        "ablation": 80,    # Réduit de 100 à 80
        "individual": 25,  # Réduit de 30 à 25
        "grouped": 18      # Réduit de 20 à 18
    }

    base = base_trials.get(optimization_type, 100)
    optimal_trials = int(base * horizon_factor * data_factor)

    # Limites min/max
    min_trials = {"harsh": 50, "sharp": 30, "ablation": 30, "individual": 10, "grouped": 8}
    max_trials = {"harsh": 300, "sharp": 150, "ablation": 150, "individual": 50, "grouped": 40}

    optimal_trials = max(min_trials.get(optimization_type, 20), optimal_trials)
    optimal_trials = min(max_trials.get(optimization_type, 200), optimal_trials)

    return optimal_trials

# Constantes dynamiques (utilisées comme fallback)
HARSH_PARAMETER_OPTIM = 180  # Réduit de 200
INDIVIDUAL_FEATURE_TRIAL = 25  # Réduit de 30
GROUPED_FEATURE_TRIAL = 18     # Réduit de 20
SHARP_PARAMETERS_OPTIM = 80    # Réduit de 100
ABLATION_TRIALS = 80           # Réduit de 100


SEED = 42
random.seed(SEED)
np.random.seed(SEED)
tf.random.set_seed(SEED)


INDICATOR_CONFIG_LARGE = {
    "RSI": {"min_factor": 2, "max_factor": 8},
    "RSI_delta_medium": {"min_factor": 10, "max_factor": 25},

    "StochRSI_period": {"min_factor": 4, "max_factor": 14},      # période de base
    "StochRSI_fastk": {"min_factor": 2, "max_factor": 8},        # souvent 3
    "StochRSI_fastd": {"min_factor": 2, "max_factor": 8},        # souvent 3

    "TEMA": {"min_factor": 3, "max_factor": 10},                 # plus lisse qu’EMA, période un peu plus longue

    "CMO": {"min_factor": 2, "max_factor": 8},
    "Sharpe_local": {"min_factor": 2, "max_factor": 8},

    "MACD_fast": {"min_factor": 2, "max_factor": 8},
    "MACD_slow": {"min_factor": 2, "max_factor": 8},
    "MACD_signal": {"min_factor": 2, "max_factor": 8},
    "MACD_delta_medium": {"min_factor": 10, "max_factor": 20},

    "EMA": {"min_factor": 2, "max_factor": 8},

    "ADX": {"min_factor": 2, "max_factor": 8},
    "ATR": {"min_factor": 2, "max_factor": 8},
    "CMF": {"min_factor": 2, "max_factor": 8},
    "CCI": {"min_factor": 2, "max_factor": 8},
    "MFI": {"min_factor": 2, "max_factor": 8},

    "VWAP": {"min_factor": 3, "max_factor": 10},                 # période de roulage du VWAP

    "Z_Score": {"min_factor": 3, "max_factor": 10},              # période pour le Z-Score

    "Momentum": {"min_factor": 2, "max_factor": 8},
    "Momentum_delta_medium": {"min_factor": 10, "max_factor": 25},

    # Nouveaux indicateurs
    "Williams_R": {"min_factor": 2, "max_factor": 8},
    "VROC": {"min_factor": 2, "max_factor": 8},
    "Aroon": {"min_factor": 2, "max_factor": 8},
    "UltOsc_fast": {"min_factor": 1, "max_factor": 3},
    "UltOsc_medium": {"min_factor": 2, "max_factor": 6},
    "UltOsc_slow": {"min_factor": 4, "max_factor": 12},
    "Donchian": {"min_factor": 3, "max_factor": 10},
    "Keltner": {"min_factor": 3, "max_factor": 10}
}


INDICATOR_GROUPS = {
    "MACD": {
        "features": ["MACD", "MACD_medium_delta", "MACD_medium_pct", "MACD_medium_mean"],
        "params": ["MACD_fast", "MACD_slow", "MACD_signal", "MACD_delta_medium"]
    },
    "RSI": {
        "features": ["RSI", "RSI_medium_delta", "RSI_medium_pct", "RSI_medium_mean"],
        "params": ["RSI", "RSI_delta_medium"]
    },
    "CMO": {
        "features": ["CMO"],
        "params": ["CMO"]
    },
    "Sharpe": {
        "features": ["Sharpe_local"],
        "params": ["Sharpe_local"]
    },
    "Momentum": {
        "features": ["Momentum", "Momentum_medium_delta", "Momentum_medium_pct", "Momentum_medium_mean"],
        "params": ["Momentum", "Momentum_delta_medium"]
    },
    "CCI": {
        "features": ["CCI"],
        "params": ["CCI"]
    },
    "CMF": {
        "features": ["CMF"],
        "params": ["CMF"]
    },
    "EMA": {
        "features": ["EMA"],
        "params": ["EMA"]
    },
    "BBANDS": {
        "features": ["Percent_B"],
        "params": ["BBANDS"]
    },
    "ATR": {
        "features": ["ATR"],
        "params": ["ATR"]
    },
    "ADX": {
        "features": ["ADX"],
        "params": ["ADX"]
    },
    "MFI": {
        "features": ["MFI"],
        "params": ["MFI"]
    },
    "StochRSI": {
        "features": ["StochRSI_K", "StochRSI_D", "StochRSI_CrossUp"],
        "params": ["StochRSI_period", "StochRSI_fastk", "StochRSI_fastd"]
    },
    "TEMA": {
        "features": ["TEMA"],
        "params": ["TEMA"]
    },
    "ZScore": {
        "features": ["Z_Score", "Z_Score_abs"],
        "params": ["Z_Score"]
    },
    "VWAP": {
        "features": ["VWAP", "VWAP_Diff"],
        "params": ["VWAP"]
    },

    # Nouveaux indicateurs
    "Williams_R": {
        "features": ["Williams_R", "Williams_R_oversold", "Williams_R_overbought"],
        "params": ["Williams_R"]
    },
    "PSAR": {
        "features": ["PSAR_trend", "PSAR_distance"],
        "params": []  # PSAR n'a pas de paramètres configurables
    },
    "VROC": {
        "features": ["VROC", "VROC_positive"],
        "params": ["VROC"]
    },
    "Aroon": {
        "features": ["Aroon_Up", "Aroon_Down", "Aroon_Oscillator", "Aroon_trend_strength"],
        "params": ["Aroon"]
    },
    "Ultimate_Oscillator": {
        "features": ["Ultimate_Oscillator", "UltOsc_oversold", "UltOsc_overbought"],
        "params": ["UltOsc_fast", "UltOsc_medium", "UltOsc_slow"]
    },
    "Donchian": {
        "features": ["Donchian_Position", "Donchian_Breakout_Up", "Donchian_Breakout_Down"],
        "params": ["Donchian"]
    },
    "Keltner": {
        "features": ["Keltner_Position", "Keltner_Squeeze"],
        "params": ["Keltner"]
    }
}


INDICATOR_GROUPED = {
    "oscillators": {
        "features": ["RSI", "RSI_medium_delta", "RSI_medium_pct", "RSI_medium_mean", "StochRSI_K", "StochRSI_D", "StochRSI_CrossUp", "CCI", "MFI", "CMO", "Williams_R", "Williams_R_oversold", "Williams_R_overbought", "Ultimate_Oscillator", "UltOsc_oversold", "UltOsc_overbought"],
        "params": ["RSI", "RSI_delta_medium", "StochRSI_period", "StochRSI_fastk", "StochRSI_fastd", "CCI", "MFI", "CMO", "Williams_R", "UltOsc_fast", "UltOsc_medium", "UltOsc_slow"]
    },
    "trend": {
        "features": ["MACD", "MACD_medium_delta", "MACD_medium_pct", "MACD_medium_mean", "EMA", "ADX", "Momentum", "Momentum_medium_delta", "Momentum_medium_pct", "Momentum_medium_mean", "TEMA", "PSAR_trend", "PSAR_distance", "Aroon_Up", "Aroon_Down", "Aroon_Oscillator", "Aroon_trend_strength"],
        "params": ["MACD_fast", "MACD_slow", "MACD_signal", "MACD_delta_medium", "EMA", "ADX", "Momentum", "Momentum_delta_medium", "TEMA", "Aroon"]
    },
    "volume": {
        "features": ["CMF", "VWAP", "VWAP_Diff", "VROC", "VROC_positive"],
        "params": ["CMF", "VWAP", "VROC"]
    },
    "volatility": {
        "features": ["ATR", "Z_Score", "Z_Score_abs", "Percent_B", "Donchian_Position", "Donchian_Breakout_Up", "Donchian_Breakout_Down", "Keltner_Position", "Keltner_Squeeze"],
        "params": ["ATR", "Z_Score", "BBANDS", "Donchian", "Keltner"]
    },
    "performance": {
        "features": ["Sharpe_local"],
        "params": ["Sharpe_local"]
    }
}


import tracemalloc
from pympler import muppy, summary

tracemalloc.start()

def log_memory_snapshot(uid, context="global"):
    try:
        # Tracemalloc - Top 5 lignes
        snapshot = tracemalloc.take_snapshot()
        top_stats = snapshot.statistics('lineno')

        logger.send_log(f"📊 - {uid} - {context} - Top 5 lignes les plus gourmandes :", "debug")
        for stat in top_stats[:5]:
            logger.send_log(f"📍 - {uid} - {context} - {stat}", "debug")

        # Pympler - résumé objets Python
        all_objects = muppy.get_objects()
        sum_obj = summary.summarize(all_objects)

        top_lines = list(summary.format_(sum_obj))
        for line in top_lines[:5]:  # on limite pour éviter le flood
            logger.send_log(f"📦 - {uid} - {context} - {line}", "debug")

        # RAM brute (RSS)
        import psutil, os
        mem = psutil.Process(os.getpid()).memory_info().rss / 1024**2
        logger.send_log(f"🧠 - {uid} - {context} - RSS total : {mem} MB", "info")

        gc.collect()
    except Exception as e:
        logger.send_log(f"❌ - {uid} - {context} - Erreur snapshot memory : {e}", "error")



def time_series_cross_validation(trial, model_params, df, horizon, uid, n_splits=3):
    """
    Validation croisée temporelle pour éviter le data leakage et améliorer la robustesse.

    Args:
        trial: Optuna trial
        model_params: Paramètres du modèle
        df: DataFrame avec les données
        horizon: Configuration du modèle
        uid: Identifiant unique
        n_splits: Nombre de splits temporels

    Returns:
        tuple: (score_moyen, score_std, scores_individuels)
    """
    try:
        # Vérifier qu'on a assez de données
        min_samples_per_split = 500
        if len(df) < min_samples_per_split * (n_splits + 1):
            # Réduire le nombre de splits si pas assez de données
            n_splits = max(2, len(df) // min_samples_per_split - 1)
            if n_splits < 2:
                # Fallback sur validation simple
                return models.optimize_model_regression_simple_split(
                    trial, horizon, uid, df, model_params, strict_pruning=True, baseline_score=0
                )

        scores = []
        split_size = len(df) // (n_splits + 1)

        base_log = f"{uid} - {horizon['name']} - CV"
        logger.send_log(f"🔄 - {base_log} - Début validation croisée temporelle ({n_splits} splits)", "info")

        for i in range(n_splits):
            # Split temporel : train sur le passé, validation sur le futur
            train_end = split_size * (i + 2)  # +2 pour avoir assez de données d'entraînement
            val_start = train_end - split_size // 2  # Overlap réduit pour éviter le leakage
            val_end = train_end + split_size // 2

            # S'assurer qu'on ne dépasse pas les limites
            val_end = min(val_end, len(df))
            if val_start >= val_end:
                continue

            train_data = df.iloc[:train_end].copy()
            val_data = df.iloc[val_start:val_end].copy()

            # Vérifier qu'on a assez de données
            if len(train_data) < 200 or len(val_data) < 50:
                continue

            try:
                # Créer un trial temporaire pour ce split
                class TempTrial:
                    def __init__(self, parent_trial):
                        self.parent = parent_trial
                        self.user_attrs = {}
                        self.number = f"{parent_trial.number}_split_{i}"

                    def set_user_attr(self, key, value):
                        self.user_attrs[key] = value

                    def report(self, value, step=None):
                        pass

                    def should_prune(self):
                        return False

                temp_trial = TempTrial(trial)
                temp_trial.set_user_attr("model_params", model_params)

                # Évaluation sur ce split
                score = models.evaluate_model_on_split(
                    temp_trial, horizon, uid, train_data, val_data, model_params
                )

                if score is not None and not np.isnan(score):
                    scores.append(score)
                    logger.send_log(f"📊 - {base_log} - Split {i+1}/{n_splits}: {score:.4f}", "debug")

                # Nettoyage
                del train_data, val_data, temp_trial

            except Exception as e:
                logger.send_log(f"⚠️ - {base_log} - Erreur split {i+1}: {e}", "warning")
                continue

        if len(scores) < 2:
            # Fallback sur validation simple si pas assez de scores
            logger.send_log(f"⚠️ - {base_log} - Pas assez de splits valides, fallback validation simple", "warning")
            return models.optimize_model_regression_simple_split(
                trial, horizon, uid, df, model_params, strict_pruning=True, baseline_score=0
            )

        # Calcul des statistiques
        mean_score = np.mean(scores)
        std_score = np.std(scores)

        # Pénaliser la variance élevée (modèles instables)
        stability_penalty = std_score * 0.1  # 10% de pénalité pour chaque point d'écart-type
        adjusted_score = mean_score - stability_penalty

        logger.send_log(f"📈 - {base_log} - CV terminée: {mean_score:.4f} ± {std_score:.4f} (ajusté: {adjusted_score:.4f})", "info")

        # Stocker les métriques détaillées
        trial.set_user_attr("cv_mean_score", mean_score)
        trial.set_user_attr("cv_std_score", std_score)
        trial.set_user_attr("cv_adjusted_score", adjusted_score)
        trial.set_user_attr("cv_individual_scores", scores)
        trial.set_user_attr("cv_n_splits", len(scores))

        return adjusted_score

    except Exception as e:
        logger.send_log(f"❌ - {uid} - Erreur validation croisée: {e}", "error")
        # Fallback sur validation simple
        return models.optimize_model_regression_simple_split(
            trial, horizon, uid, df, model_params, strict_pruning=True, baseline_score=0
        )

def log_study_statistics(study, base_log):
    """
    Loggue les statistiques détaillées d'une étude Optuna.
    """
    total_trials = len(study.trials)
    completed_trials = [t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE]
    pruned_trials = [t for t in study.trials if t.state == optuna.trial.TrialState.PRUNED]
    failed_trials = [t for t in study.trials if t.state == optuna.trial.TrialState.FAIL]
    importances = optuna.importance.get_param_importances(study)

    logger.send_log(f"📊 - {base_log} - Statistiques Optuna sur {total_trials} trials :", "info")
    logger.send_log(f"✅ - {base_log} - Complétés : {len(completed_trials)} | 🚫 Prunés : {len(pruned_trials)} | ❌ Échoués : {len(failed_trials)}", "debug")

    if total_trials > 0:
        pruning_rate = 100 * len(pruned_trials) / total_trials
        fail_rate = 100 * len(failed_trials) / total_trials
        logger.send_log(f"📉 - {base_log} - Taux de pruning : {pruning_rate:.1f} % | Taux d'échec : {fail_rate:.1f} %", "debug")

    if completed_trials:
        scores = [t.value for t in completed_trials if t.value is not None]
        if scores:
            avg_score = sum(scores) / len(scores)
            max_score = max(scores)
            min_score = min(scores)
            logger.send_log(f"🏅 - {base_log} - Score moyen : {avg_score:.2f} | Meilleur : {max_score:.2f} | Pire : {min_score:.2f}", "debug")

        best = study.best_trial
        logger.send_log(f"🥇 - {base_log} - Meilleur trial #{best.number} - Score : {best.value:.2f}", "debug")
        if hasattr(best, "duration"):
            logger.send_log(f"⏱️ - {base_log} - Durée du meilleur trial : {best.duration}", "debug")

        if best.user_attrs.get("evaluation"):
            logger.send_log(f"🧮 - {base_log} - Métriques du meilleur trial : {best.user_attrs['evaluation']}", "debug")

        logger.send_log(f"🧮 - {base_log} - Paramètres les plus influents : {importances}", "debug")





def diagnose_log_return(df, uid, horizon_hour):
    """
    Analyse rapide du log_return pour vérifier que la target est exploitable.
    Logge une alerte si bruit excessif, tendance nulle, ou suppression excessive.
    """
    try:
        log_return = df["log_return"].dropna()

        mean_lr = log_return.mean()
        std_lr = log_return.std()
        min_lr = log_return.min()
        max_lr = log_return.max()
        proportion_zeros = (log_return == 0).mean()

        base_log = f"{uid} - {horizon_hour}h - log_return"

        # 📢 Log du résumé des stats
        logger.send_log(f"📊 - {base_log} - Mean: {mean_lr:.6e}, Std: {std_lr:.6e}, Min: {min_lr:.6e}, Max: {max_lr:.6e}, Zeros: {proportion_zeros*100:.2f}%", "info")

        # 🛑 Alertes automatiques
        if abs(mean_lr) > 5e-3:
            logger.send_log(f"⚠️ - {base_log} - Moyenne non nulle (mean={mean_lr:.6e}) ➔ possible biais dans target.", "warning")

        if std_lr < 1e-4:
            logger.send_log(f"⚠️ - {base_log} - Std trop faible (std={std_lr:.6e}) ➔ target trop plate, risque de modèle paresseux.", "warning")

        if proportion_zeros > 0.6:
            logger.send_log(f"⚠️ - {base_log} - Proportion de 0 trop élevée ({proportion_zeros*100:.2f}%) ➔ perte de signal dans target.", "warning")

    except Exception as e:
        import traceback
        err_msg = traceback.format_exc()
        logger.send_log(f"❌ - {uid} - Erreur pendant diagnose_log_return :\n{err_msg}", "error")


class DummyTrial:
    number = -1  # utile pour afficher Trial -1 dans les logs
    TrialPruned = optuna.exceptions.TrialPruned  # permet de lever cette exception sans planter

    def set_user_attr(self, *args, **kwargs): pass
    def report(self, *args, **kwargs): pass
    def should_prune(self): return False


def get_n_steps_range(horizon_hour):
    points_per_hour = 60 // 5  # 5 min interval
    horizon_points = horizon_hour * points_per_hour
    max_steps = int(horizon_points * 0.7)  # 70% du shift max
    min_steps = int(horizon_points * 0.3)  # 30% du shift max
    return (min_steps, max_steps)



def remove_highly_correlated_features(df, features, threshold=0.95, protected_columns=None):
    """
    Supprime les features très corrélées (au-delà du seuil spécifié),
    sauf celles explicitement protégées.

    :param df: DataFrame contenant les données.
    :param features: Liste des colonnes à analyser.
    :param threshold: Seuil de corrélation à partir duquel supprimer.
    :param protected_columns: Liste de colonnes à ne jamais supprimer.
    :return: (features conservées, paires corrélées)
    """
    protected_columns = protected_columns or []

    corr_matrix = df[features].corr().abs()
    upper = corr_matrix.where(np.triu(np.ones(corr_matrix.shape), k=1).astype(bool))

    to_drop = []
    correlated_pairs = []

    for col in upper.columns:
        correlated = upper[col][upper[col] > threshold].index.tolist()
        if correlated:
            correlated_pairs.append((col, correlated))
            if col not in protected_columns:
                to_drop.append(col)

    kept_features = [f for f in features if f not in to_drop]

    del df
    return kept_features, correlated_pairs



def suggest_global_params(trial, best_periods):
    params = {}

    for name, cfg in INDICATOR_CONFIG_LARGE.items():

        if name not in best_periods:
            logger.send_log(f"🟡 - {name} not in best_periods, pruning", "warning")
            raise optuna.TrialPruned()

        low = max(2, int(best_periods[name] - cfg["min_factor"]))
        high = int(best_periods[name] + cfg["max_factor"])
        # On ne gère pas encore les contraintes, on stocke simplement les bornes
        params[name] = trial.suggest_int(name, low, high)

    # ✅ Contraintes logiques (ex : MACD_fast < MACD_slow)
    if params["MACD_fast"] >= params["MACD_slow"]:
        fast = min(params["MACD_fast"], params["MACD_slow"] - 1)
        slow = max(params["MACD_fast"] + 1, params["MACD_slow"])
        params["MACD_fast"] = fast
        params["MACD_slow"] = slow


    return params




def get_full_default_params(horizon_hour):
    """
    Génère les paramètres par défaut pour tous les indicateurs selon l'horizon.

    Args:
        horizon_hour: Horizon de prédiction en heures

    Returns:
        dict: Paramètres par défaut pour tous les indicateurs
    """
    full_params = {}
    for name, cfg in INDICATOR_CONFIG_LARGE.items():
        avg_factor = (cfg["min_factor"] + cfg["max_factor"]) / 2
        full_params[name] = int(horizon_hour * avg_factor)

    # Contraintes logiques pour MACD
    if full_params["MACD_fast"] >= full_params["MACD_slow"]:
        full_params["MACD_fast"] = max(2, full_params["MACD_slow"] - 1)

    return full_params


def merge_indicator_params(existing_params, horizon_hour):
    """
    Fusionne intelligemment les paramètres existants avec les nouveaux indicateurs.

    Args:
        existing_params: Paramètres existants depuis la DB (peut être incomplet)
        horizon_hour: Horizon pour calculer les paramètres par défaut des nouveaux indicateurs

    Returns:
        dict: Paramètres complets (existants + nouveaux avec valeurs par défaut)
    """
    # 1. Commencer avec tous les paramètres par défaut
    complete_params = get_full_default_params(horizon_hour)

    # 2. Remplacer par les valeurs existantes si disponibles
    if existing_params:
        for param_name, param_value in existing_params.items():
            if param_name in complete_params:
                complete_params[param_name] = param_value

    # 3. Vérifier les contraintes logiques après fusion
    if complete_params["MACD_fast"] >= complete_params["MACD_slow"]:
        complete_params["MACD_fast"] = max(2, complete_params["MACD_slow"] - 1)

    # 4. Log des nouveaux indicateurs ajoutés
    if existing_params:
        new_indicators = set(complete_params.keys()) - set(existing_params.keys())
        if new_indicators:
            logger.send_log(f"🆕 Nouveaux indicateurs ajoutés avec valeurs par défaut : {list(new_indicators)}", "info")

    return complete_params


def suggest_indicator_params(trial, horizon):
    params = {}

    for name, cfg in INDICATOR_CONFIG_LARGE.items():
        low = int(horizon * cfg["min_factor"])
        high = int(horizon * cfg["max_factor"])
        # On ne gère pas encore les contraintes, on stocke simplement les bornes
        params[name] = trial.suggest_int(name, low, high)

    # ✅ Contraintes logiques (ex : MACD_fast < MACD_slow)
    if params["MACD_fast"] >= params["MACD_slow"]:
        fast = min(params["MACD_fast"], params["MACD_slow"] - 1)
        slow = max(params["MACD_fast"] + 1, params["MACD_slow"])
        params["MACD_fast"] = fast
        params["MACD_slow"] = slow

    return params


def get_model_specific_params(trial, model_type, horizon_hour):
    """
    Génère les paramètres spécifiques selon le type de modèle choisi.

    Args:
        trial: Optuna trial object
        model_type: Type de modèle ('basic', 'enhanced', 'high_performance')
        horizon_hour: Horizon de prédiction en heures

    Returns:
        dict: Paramètres spécifiques au modèle
    """
    params = {}

    if model_type == "basic":
        # Paramètres pour build_cnn_attention_lstm_model_test
        params.update({
            # CNN parameters
            "use_conv": trial.suggest_categorical("use_conv", [True]),
            "conv_filters": trial.suggest_categorical("conv_filters", [32, 64, 128, 256]),
            "kernel_size": trial.suggest_categorical("kernel_size", [2, 3, 4, 5]),
            "pool_size": trial.suggest_categorical("pool_size", [2, 3]),
            "use_batch_norm": trial.suggest_categorical("use_batch_norm", [True, False]),
            "activation": trial.suggest_categorical("activation", ['relu', 'elu', 'selu', 'gelu']),

            # LSTM parameters
            "lstm_units_1": trial.suggest_categorical("lstm_units_1", [32, 64, 96, 128]),
            "lstm_units_2": trial.suggest_categorical("lstm_units_2", [32, 64, 96, 128]),
            "lstm_dropout": trial.suggest_float("lstm_dropout", 0.1, 0.4),
            "use_bidirectional": trial.suggest_categorical("use_bidirectional", [True, False]),

            # Attention parameters
            "use_attention": trial.suggest_categorical("use_attention", [True, False]),
            "mha_heads": trial.suggest_categorical("mha_heads", [2, 4, 8]),
            "mha_key_dim": trial.suggest_categorical("mha_key_dim", [16, 32, 64]),
            "mha_dropout": trial.suggest_float("mha_dropout", 0.0, 0.2),
        })

    elif model_type == "enhanced":
        # Paramètres pour build_enhanced_cnn_attention_lstm_model
        params.update({
            # CNN parameters (multi-scale)
            "conv_filters": trial.suggest_categorical("conv_filters", [64, 96, 128, 192]),
            "use_batch_norm": trial.suggest_categorical("use_batch_norm", [True, False]),

            # LSTM parameters (bidirectional par défaut)
            "lstm_units_1": trial.suggest_categorical("lstm_units_1", [64, 96, 128, 160]),
            "lstm_units_2": trial.suggest_categorical("lstm_units_2", [96, 128, 160, 192]),
            "dropout_rate": trial.suggest_float("dropout_rate", 0.1, 0.4),

            # Multi-head attention parameters
            "mha_heads": trial.suggest_categorical("mha_heads", [2, 4, 6, 8]),
            "mha_key_dim": trial.suggest_categorical("mha_key_dim", [16, 32, 48, 64]),

            # Feature fusion parameters
            "amplitude_scale": trial.suggest_float("amplitude_scale", 0.05, 0.2),
        })

    elif model_type == "high_performance":
        # Paramètres pour build_high_performance_model (adaptatifs automatiques)
        # Ce modèle adapte automatiquement ses paramètres selon l'horizon
        # On peut quand même optimiser quelques paramètres de base
        params.update({
            # Les paramètres de base sont calculés automatiquement selon l'horizon
            # On peut optimiser les facteurs de scaling
            "base_filters_factor": trial.suggest_float("base_filters_factor", 0.8, 1.2),
            "lstm_units_factor": trial.suggest_float("lstm_units_factor", 0.8, 1.2),
            "dropout_factor": trial.suggest_float("dropout_factor", 0.8, 1.2),
        })

    return params

def get_common_params_enhanced(trial, horizon_hour):
    """
    Génère les paramètres communs à tous les modèles avec adaptation selon l'horizon.

    Args:
        trial: Optuna trial object
        horizon_hour: Horizon de prédiction en heures

    Returns:
        dict: Paramètres communs
    """
    min_steps, max_steps = get_n_steps_range(horizon_hour)
    min_trend_lag = max(1, int(horizon_hour / 12))
    max_trend_lag = max(2, int(horizon_hour / 6))

    # Adaptation des plages selon l'horizon
    if horizon_hour <= 6:  # Court terme
        lr_min, lr_max = 1e-3, 5e-3
        epochs_min, epochs_max = 20, 60
    elif horizon_hour <= 24:  # Moyen terme
        lr_min, lr_max = 5e-4, 2e-3
        epochs_min, epochs_max = 15, 50
    else:  # Long terme
        lr_min, lr_max = 1e-4, 1e-3
        epochs_min, epochs_max = 10, 40

    # Espace de valeurs FIXE pour éviter l'erreur "dynamic value space"
    batch_sizes = [32, 64, 96, 128, 192]  # Espace unifié pour tous les horizons

    return {
        # Paramètres de données
        "n_steps": trial.suggest_int("n_steps", min_steps, max_steps),

        # Paramètres d'entraînement
        "learning_rate": trial.suggest_float("learning_rate", lr_min, lr_max, log=True),
        "batch_size": trial.suggest_categorical("batch_size", batch_sizes),
        "epochs": trial.suggest_int("epochs", epochs_min, epochs_max),
        "patience": trial.suggest_int("patience", 3, 8),
        "use_reduce_lr": trial.suggest_categorical("use_reduce_lr", [True, False]),

        # Régularisation
        "output_l2_reg": trial.suggest_loguniform("output_l2_reg", 1e-6, 1e-2),
        "weight_decay": trial.suggest_loguniform("weight_decay", 1e-6, 1e-2),

        # Paramètres de fonction de perte (si pas d'adaptation automatique)
        "use_adaptive_params": trial.suggest_categorical("use_adaptive_params", [True, False]),
        "lambda_std_val": trial.suggest_float("lambda_std_val", 1.0, 100.0),
        "lambda_scale_val": trial.suggest_float("lambda_scale_val", 1.0, 50.0),
        "lambda_sign_val": trial.suggest_float("lambda_sign_val", 0.0, 5.0),
        "trend_lag": trial.suggest_int("trend_lag", min_trend_lag, max_trend_lag),
    }

def get_loss_specific_params_enhanced(trial, horizon_hour, use_adaptive_params):
    """
    Génère les paramètres spécifiques aux fonctions de perte si l'adaptation automatique est désactivée.

    Args:
        trial: Optuna trial object
        horizon_hour: Horizon de prédiction en heures
        use_adaptive_params: Si True, utilise les paramètres adaptatifs automatiques

    Returns:
        dict: Paramètres de fonction de perte
    """
    if use_adaptive_params:
        # Les paramètres seront calculés automatiquement
        return {}

    # Paramètres manuels selon l'horizon avec espace uniforme
    if horizon_hour <= 3:
        gamma_min, gamma_max = 0.8, 0.95
        alpha_min, alpha_max = 0.5, 0.8
        w_pearson_min, w_pearson_max = 0.0, 0.0  # Forcé à 0 pour court terme
    elif horizon_hour <= 12:
        gamma_min, gamma_max = 0.65, 0.9
        alpha_min, alpha_max = 0.3, 0.7
        w_pearson_min, w_pearson_max = 0.0, 0.25
    else:  # Long terme
        gamma_min, gamma_max = 0.5, 0.8
        alpha_min, alpha_max = 0.2, 0.6
        w_pearson_min, w_pearson_max = 0.05, 0.3

    # Toujours créer tous les paramètres pour éviter l'espace dynamique
    params = {
        "gamma_val": trial.suggest_float("gamma_val", gamma_min, gamma_max),
        "alpha_val": trial.suggest_float("alpha_val", alpha_min, alpha_max),
        "w_pearson": trial.suggest_float("w_pearson", w_pearson_min, w_pearson_max),
    }

    return params

def should_stop_optimization(study, min_trials=30, improvement_threshold=0.01):
    """
    Détermine si l'optimisation doit s'arrêter prématurément.

    Args:
        study: Étude Optuna
        min_trials: Nombre minimum de trials avant d'envisager l'arrêt
        improvement_threshold: Seuil d'amélioration minimum (1% par défaut)

    Returns:
        bool: True si l'optimisation doit s'arrêter
    """
    if len(study.trials) < min_trials:
        return False

    # Analyser les derniers 30% des trials
    recent_ratio = 0.3
    recent_count = max(10, int(len(study.trials) * recent_ratio))
    recent_trials = study.trials[-recent_count:]

    # Scores des trials récents
    recent_scores = [t.value for t in recent_trials if t.value is not None and t.state == optuna.trial.TrialState.COMPLETE]

    if len(recent_scores) < 5:
        return False

    # Meilleur score récent vs meilleur score global
    best_recent = max(recent_scores)
    best_overall = study.best_value

    if best_overall == 0:
        return False

    # Calculer l'amélioration relative
    improvement = (best_recent - best_overall) / abs(best_overall)

    # Arrêter si pas d'amélioration significative
    return improvement < improvement_threshold

def optimize_model_parameters_enhanced(uid, model, df, baseline_score=0, previous_model_params=None, n_trials=10):
    """
    Version améliorée qui optimise les paramètres du modèle avec adaptation selon le type de modèle choisi.
    Supporte les modèles: basic, enhanced, high_performance.
    Inclut validation croisée temporelle, pruning agressif et early stopping.

    Returns:
        dict: Meilleurs paramètres du modèle trouvés
    """
    base_log = f"{uid} - {model['name']}"
    horizon_hour = model['horizon_hour']
    indicator_params = model.get("indicator_params", {})

    # 🚀 Configuration des performances optimisée (TensorFlow uniquement)
    perf_optimizer = PerformanceOptimizer(verbose=False)
    perf_config = perf_optimizer.get_optimal_optuna_config(n_trials, len(df))

    # Calcul adaptatif du nombre de trials
    adaptive_trials = get_adaptive_trials(horizon_hour, "harsh", len(df))
    actual_trials = min(perf_config['n_trials'], adaptive_trials)  # Utiliser la config optimisée

    # ⚠️ IMPORTANT: Garder n_jobs=1 pour préserver l'apprentissage séquentiel d'Optuna
    # La parallélisation nuirait à l'efficacité du TPESampler qui apprend des trials précédents
    n_jobs_optimal = 1

    logger.send_log(f"🎯 - {base_log} - Trials adaptatifs: {adaptive_trials} (demandé: {n_trials}, utilisé: {actual_trials})", "info")
    logger.send_log(f"🧠 - {base_log} - Mode séquentiel préservé pour l'apprentissage Optuna", "info")

    # Optimisation mono-objectif sur combined_score (maximize) avec pruning agressif
    n_startup_trials = perf_config['n_startup_trials']
    pruner_config = perf_config['pruner_config']

    study = optuna.create_study(
        direction="maximize",
        study_name=f"model_params_opt_enhanced_{model['name']}_{horizon_hour}",
        sampler=optuna.samplers.TPESampler(
            n_startup_trials=n_startup_trials,
            multivariate=True,
            constant_liar=True  # Améliore la parallélisation
        ),
        pruner=optuna.pruners.MedianPruner(
            n_startup_trials=pruner_config['n_startup_trials'],
            n_warmup_steps=pruner_config['n_warmup_steps'],
            interval_steps=pruner_config['interval_steps']
        )
    )
    study.set_user_attr("startup_trials", n_startup_trials)

    # Enqueue des paramètres précédents (non bloquant avec validation)
    if previous_model_params:
        try:
            # Filtrer les paramètres pour éviter les conflits avec l'espace de paramètres actuel
            filtered_params = {}

            # Définir les plages valides pour les paramètres problématiques
            # Unifier toutes les plages possibles pour éviter les conflits
            valid_ranges = {
                "mha_heads": [2, 4, 6, 8],  # Union de toutes les plages possibles
                "conv_filters": [32, 64, 96, 128, 192, 256],  # Union basic + enhanced
                "lstm_units_1": [32, 64, 96, 128, 160],  # Union basic + enhanced
                "lstm_units_2": [32, 64, 96, 128, 160, 192],  # Union basic + enhanced
                "mha_key_dim": [16, 32, 48, 64],  # Union basic + enhanced
                "kernel_size": [2, 3, 4, 5],  # Plage basic
                "pool_size": [2, 3],
                "activation": ['relu', 'elu', 'selu', 'gelu'],
                "use_conv": [True],
                "use_batch_norm": [True, False],
                "use_bidirectional": [True, False],
                "use_attention": [True, False],
                "model_type": ["basic", "enhanced", "high_performance"],
                "batch_size": [32, 64, 96, 128, 192],
                "use_reduce_lr": [True, False],
                "use_adaptive_params": [True, False]
            }

            # Copier les paramètres en validant les valeurs
            for key, value in previous_model_params.items():
                if key in valid_ranges:
                    if value in valid_ranges[key]:
                        filtered_params[key] = value
                    else:
                        # Utiliser la valeur la plus proche
                        if isinstance(value, (int, float)) and isinstance(valid_ranges[key][0], (int, float)):
                            closest = min(valid_ranges[key], key=lambda x: abs(x - value))
                            filtered_params[key] = closest
                            logger.send_log(f"🔧 - {base_log} - Paramètre {key} ajusté de {value} à {closest}", "debug")
                        else:
                            filtered_params[key] = valid_ranges[key][0]  # Valeur par défaut
                            logger.send_log(f"🔧 - {base_log} - Paramètre {key} remplacé par défaut: {valid_ranges[key][0]}", "debug")
                else:
                    # Paramètres non problématiques
                    filtered_params[key] = value

            # Tenter d'enqueue les paramètres filtrés
            study.enqueue_trial(filtered_params)
            logger.send_log(f"✅ - {base_log} - Paramètres précédents enqueuées avec succès", "info")

        except Exception as e:
            logger.send_log(f"⚠️ - {base_log} - Impossible d'enqueue les paramètres précédents: {e}", "warning")
            logger.send_log(f"🔄 - {base_log} - Optuna utilisera uniquement ses suggestions", "info")

    def objective(trial):
        try:
            # 1. Choix du type de modèle
            model_type = trial.suggest_categorical("model_type", ["basic", "enhanced", "high_performance"])

            # 2. Paramètres communs
            model_params = get_common_params_enhanced(trial, horizon_hour)

            # 3. Paramètres spécifiques au modèle
            model_specific = get_model_specific_params(trial, model_type, horizon_hour)
            model_params.update(model_specific)

        except Exception as param_error:
            logger.send_log(f"❌ - {base_log} - Erreur lors de la génération des paramètres: {param_error}", "error")
            # Retourner un score très faible pour que ce trial soit ignoré
            return 0.0

        # 4. Paramètres de fonction de perte
        use_adaptive_params = model_params.get("use_adaptive_params", True)
        loss_params = get_loss_specific_params_enhanced(trial, horizon_hour, use_adaptive_params)
        model_params.update(loss_params)

        # 5. Ajout des métadonnées
        model_params.update({
            "model_type": model_type,
            "horizon_hour": horizon_hour,
        })

        # 6. Choix de la fonction de perte - EXPLORATION COMPLÈTE (sans contraintes par modèle)
        all_loss_types = [
            "anti_suppression",
            "balanced_reward",
            "correlation_mae_amplitude",
            "corr_mae_amp_deriv",
            "aggressive_loss",
            "adaptive_dual_objective",
            "correlation_huber",
            "corr_mae_amp_bias",
            "directional_loss",
            "amplitude_directional",
            "dual_objective",
            "pearson_da_amplitude",
            "amplitude_directional_reversal"
        ]

        # Exploration complètement libre - TOUTES les fonctions de perte disponibles
        # Optuna découvrira automatiquement les meilleures combinaisons modèle + loss
        model_params["loss_type"] = trial.suggest_categorical("loss_type", all_loss_types)

        # 7. Paramètres spécifiques selon le type de modèle choisi
        if model_type == "basic":
            # Paramètres pour build_cnn_attention_lstm_model_test
            model_params["use_enhanced_model"] = False
        elif model_type == "enhanced":
            # Paramètres pour build_enhanced_cnn_attention_lstm_model
            model_params["use_enhanced_model"] = True
        elif model_type == "high_performance":
            # Paramètres pour build_high_performance_model
            model_params["use_high_performance_model"] = True

        trial.set_user_attr("model_params", model_params)

        # Utiliser la validation croisée temporelle pour une évaluation plus robuste
        use_time_series_cv = len(df) > 1000  # Seulement si assez de données

        if use_time_series_cv:
            # Validation croisée temporelle (plus précise mais plus lente)
            combined = time_series_cross_validation(
                trial=trial,
                model_params=model_params,
                df=df,
                horizon=model,
                uid=uid,
                n_splits=3  # 3 splits pour équilibrer précision/vitesse
            )
        else:
            # Validation simple pour petits datasets
            models.optimize_model_regression_simple_split(
                trial=trial,
                horizon=model,
                uid=uid,
                df=df,
                model_params=model_params,
                strict_pruning=True,
                baseline_score=baseline_score
            )
            evaluation = trial.user_attrs.get("evaluation", {})
            combined = evaluation.get("combined_score", 0)

        # Nettoyage mémoire
        trial.user_attrs.clear()
        if hasattr(trial, "intermediate_values"):
            trial.intermediate_values.clear()

        gc.collect()

        return combined

    logger.send_log(f"🚀 - {base_log} - Début d'optimisation ENHANCED des paramètres du modèle avec {actual_trials} trials", "info")
    study.optimize(objective, n_trials=actual_trials, n_jobs=n_jobs_optimal)

    if study.best_trial is None:
        logger.send_log(f"❌ - {base_log} - Aucun trial n'a été validé pour les paramètres du modèle.", "error")
        return previous_model_params

    best_trial = study.best_trial
    best_model_params = best_trial.user_attrs.get("model_params", {})
    evaluation = best_trial.user_attrs.get("evaluation", {})

    log_study_statistics(study, base_log)

    # Nettoyage mémoire
    try:
        study.trials.clear()
        study._storage = None  # libération explicite
    except Exception:
        pass
    del study
    gc.collect()

    return best_model_params

def optimize_model_parameters(uid, model, df, baseline_score=0, previous_model_params=None, n_trials=10):
    """
    Optimise les paramètres du modèle CNN+Attention+LSTM via Optuna (mono-objectif sur le combined_score).

    Returns:
        dict: Meilleurs paramètres du modèle trouvés
    """
    base_log = f"{uid} - {model['name']}"
    horizon_hour = model['horizon_hour']
    indicator_params = model.get("indicator_params", {})

    # Optimisation mono-objectif sur combined_score (maximize)
    n_startup_trials = int(n_trials * 0.2)
    study = optuna.create_study(
        direction="maximize",
        study_name=f"model_params_opt_{model['name']}_{horizon_hour}",
        sampler=optuna.samplers.TPESampler(n_startup_trials=n_startup_trials, multivariate=True)
    )
    study.set_user_attr("startup_trials", n_startup_trials)

    # Enqueue des paramètres précédents (non bloquant avec validation)
    if previous_model_params:
        try:
            # Filtrer les paramètres pour éviter les conflits avec l'espace de paramètres actuel
            filtered_params = {}

            # Définir les plages valides pour les paramètres problématiques
            valid_ranges = {
                "mha_heads": [2, 4, 8, 16],  # Plages valides pour la fonction standard
                "conv_filters": [16, 32, 64, 128, 256],
                "lstm_units_1": [32, 64, 96, 128, 256, 512],
                "lstm_units_2": [32, 64, 96, 128, 256, 512],
                "mha_key_dim": [8, 16, 32, 64, 128],
                "kernel_size": [1, 2, 3, 4, 5, 7],
                "pool_size": [2, 3],
                "activation": ['relu', 'elu', 'selu', 'gelu'],
                "attention_unit": [32, 64, 96, 128, 256, 512],
                "attention_activation": ['tanh', 'relu'],
                "batch_size": [32, 64, 96, 128],
                "use_enhanced_model": [True, False],
                "use_conv": [True],
                "use_batch_norm": [True, False],
                "use_bidirectional": [True, False],
                "use_attention": [True, False],
                "use_dropout": [True],
                "use_reduce_lr": [True, False]
            }

            # Copier les paramètres en validant les valeurs
            for key, value in previous_model_params.items():
                if key in valid_ranges:
                    if value in valid_ranges[key]:
                        filtered_params[key] = value
                    else:
                        # Utiliser la valeur la plus proche ou par défaut
                        if isinstance(value, (int, float)) and isinstance(valid_ranges[key][0], (int, float)):
                            closest = min(valid_ranges[key], key=lambda x: abs(x - value))
                            filtered_params[key] = closest
                            logger.send_log(f"🔧 - {base_log} - Paramètre {key} ajusté de {value} à {closest}", "debug")
                        else:
                            filtered_params[key] = valid_ranges[key][0]  # Valeur par défaut
                            logger.send_log(f"🔧 - {base_log} - Paramètre {key} remplacé par défaut: {valid_ranges[key][0]}", "debug")
                else:
                    # Paramètres non problématiques (float ranges, etc.)
                    filtered_params[key] = value

            # Tenter d'enqueue les paramètres filtrés
            study.enqueue_trial(filtered_params)
            logger.send_log(f"✅ - {base_log} - Paramètres précédents enqueuées avec succès", "info")

        except Exception as e:
            logger.send_log(f"⚠️ - {base_log} - Impossible d'enqueue les paramètres précédents: {e}", "warning")
            logger.send_log(f"🔄 - {base_log} - Optuna utilisera uniquement ses suggestions", "info")

    def objective(trial):
        try:
            min_steps, max_steps = get_n_steps_range(model["horizon_hour"])
            min_trend_lag = int(model["shift_value"] / 12)
            max_trend_lag = int(model["shift_value"] / 6)
            if model["horizon_hour"] <= 6:  # Court terme
                lr_min = 1e-3
                lr_max = 5e-3
            elif model["horizon_hour"] <= 24:  # Moyen terme
                lr_min = 5e-4
                lr_max = 2e-3

            if model["horizon_hour"] <= 6:
                dropout_min = 0.2
                dropout_max = 0.4
            elif model["horizon_hour"] <= 24:
                dropout_min = 0.1
                dropout_max = 0.3

            if model["horizon_hour"] <= 6:
                gamma_min = 0.7
                gamma_max = 0.9
                alpha_min = 0.3
                alpha_max = 0.7
            elif model["horizon_hour"] <= 24:
                gamma_min = 0.5
                gamma_max = 0.8
                alpha_min = 0.4
                alpha_max = 0.8

            model_params = {
                "n_steps": trial.suggest_int("n_steps", min_steps, max_steps),

                # 🔧 Entraînement général
                "learning_rate": trial.suggest_float("learning_rate", lr_min, lr_max, log=True),
                "batch_size": trial.suggest_categorical("batch_size", [32, 64, 96, 128]),
                "epochs": trial.suggest_int("epochs", 15, 50),
                "patience": trial.suggest_int("patience", 2, 6),
                "use_reduce_lr": trial.suggest_categorical("use_reduce_lr", [True, False]),

                # 🚀 Choix du modèle (standard ou enhanced)
                "use_enhanced_model": trial.suggest_categorical("use_enhanced_model", [True, False]),

                # 🧱 Bloc convolutionnel/dense initial
                "use_conv": trial.suggest_categorical("use_conv", [True]),
                "conv_filters": trial.suggest_categorical("conv_filters", [16, 32, 64, 128, 256]),
                "kernel_size": trial.suggest_categorical("kernel_size", [1, 2, 3, 4, 5, 7]),
                "pool_size": trial.suggest_categorical("pool_size", [2, 3]),
                "activation": trial.suggest_categorical("activation", ['relu', 'elu', 'selu', 'gelu']),
                "use_batch_norm": trial.suggest_categorical("use_batch_norm", [True, False]),

                # 🔁 Bloc LSTM
                "lstm_units_1": trial.suggest_categorical("lstm_units_1", [32, 64, 96, 128, 256, 512]),
                "lstm_units_2": trial.suggest_categorical("lstm_units_2", [32, 64, 96, 128, 256, 512]),
                "lstm_dropout": trial.suggest_float("lstm_dropout", 0.0, 0.5),
                "use_bidirectional": trial.suggest_categorical("use_bidirectional", [True, False]),

                # 🧠 Attention multi-head
                "use_attention": trial.suggest_categorical("use_attention", [True, False]),
                "attention_unit": trial.suggest_categorical("attention_unit", [32, 64, 96, 128, 256, 512]),
                "mha_heads": trial.suggest_categorical("mha_heads", [2, 4, 8, 16]),  # Supprimé 1 pour éviter l'erreur
                "mha_key_dim": trial.suggest_categorical("mha_key_dim", [8, 16, 32, 64, 128]),
                "mha_dropout": trial.suggest_float("mha_dropout", 0.0, 0.3),

                # 💧 Dropout final
                "use_dropout": trial.suggest_categorical("use_dropout", [True]),
                "dropout_rate": trial.suggest_float("dropout_rate", dropout_min, dropout_max),
                'output_l2_reg': trial.suggest_loguniform("output_l2_reg", 1e-6, 1e-2),
                'weight_decay': trial.suggest_loguniform("weight_decay", 1e-6, 1e-2),

                # 🧠 Activation attention (utile si tu testes un autre bloc attention plus tard)
                "attention_activation": trial.suggest_categorical("attention_activation", ['tanh', 'relu']),

                # ✅ Paramètres spécifiques de la loss "adaptive_dual_objective"
                "lambda_std_val": trial.suggest_float("lambda_std_val", 1.0, 100.0), # Calibration des amplitudes
                "lambda_scale_val": trial.suggest_float("lambda_scale_val", 1.0, 50.0), # Échelle dynamique des amplitudes
                "lambda_sign_val": trial.suggest_float("lambda_sign_val", 0.0, 5.0), # Échelle dynamique des erreurs de signes
                "trend_lag": trial.suggest_int("trend_lag", min_trend_lag, max_trend_lag)
            }

            # Définition Optuna par horizon avec espace uniforme
            if model["horizon_hour"] <= 3:
                gamma_min, gamma_max = 0.8, 0.95
                alpha_min, alpha_max = 0.5, 0.8
                w_pearson_min, w_pearson_max = 0.0, 0.0  # Forcé à 0 pour court terme
            elif model["horizon_hour"] <= 12:
                gamma_min, gamma_max = 0.65, 0.9
                alpha_min, alpha_max = 0.3, 0.7
                w_pearson_min, w_pearson_max = 0.0, 0.25
            else:  # 18-24 h
                gamma_min, gamma_max = 0.5, 0.8
                alpha_min, alpha_max = 0.2, 0.6
                w_pearson_min, w_pearson_max = 0.05, 0.3

            # Toujours créer tous les paramètres pour éviter l'espace dynamique
            model_params['gamma_val'] = trial.suggest_float("gamma_val", gamma_min, gamma_max)
            model_params['alpha_val'] = trial.suggest_float("alpha_val", alpha_min, alpha_max)
            model_params['w_pearson'] = trial.suggest_float("w_pearson", w_pearson_min, w_pearson_max)

            trial.set_user_attr("model_params", model_params)

            models.optimize_model_regression_simple_split(
                trial=trial,
                horizon=model,
                uid=uid,
                df=df,
                model_params=model_params,
                strict_pruning=True,
                baseline_score=baseline_score
            )

            evaluation = trial.user_attrs.get("evaluation", {})
            combined = evaluation.get("combined_score", 0)

            # Nettoyage mémoire
            trial.user_attrs.clear()
            if hasattr(trial, "intermediate_values"):
                trial.intermediate_values.clear()

            gc.collect()

            return combined

        except Exception as param_error:
            logger.send_log(f"❌ - {base_log} - Erreur dans objective: {param_error}", "error")
            # Retourner un score très faible pour que ce trial soit ignoré
            return 0.0

    #log_memory_snapshot(uid, context=f"{model['name']} - Avant Optim")
    logger.send_log(f"🚀 - {base_log} - Début d'optimisation des paramètres du modèle avec {n_trials} trials", "info")
    study.optimize(objective, n_trials=n_trials, n_jobs=1)
    #log_memory_snapshot(uid, context=f"{model['name']} - Après Optim")

    if study.best_trial is None:
        logger.send_log(f"❌ - {base_log} - Aucun trial n’a été validé pour les paramètres du modèle.", "error")
        return previous_model_params

    best_trial = study.best_trial
    best_model_params = best_trial.user_attrs.get("model_params", {})
    evaluation = best_trial.user_attrs.get("evaluation", {})

    log_study_statistics(study, base_log)

    # Nettoyage mémoire
    del df, best_trial, evaluation
    try:
        study.trials.clear()
        study._storage = None  # libération explicite
    except Exception:
        pass
    del study
    gc.collect()

    return best_model_params



def feature_ablation_analysis(
    uid,
    model,
    candlesticks,
    model_params,
    indicator_params,
    correlation_threshold=0.95,
    baseline_score=50,
    n_trials=50
):
    base_log = f"{uid} - {model['name']}"
    excluded_features = ["log_return", "highPrice", "lowPrice", "lastPrice", "timestamp"]

    # 1. Générer le DataFrame complet avec toutes les features
    df_full = indicator.generate_training_sample(
        candlesticks, indicator_params, uid,
        model["shift_value"], model["horizon_hour"], "max", model["safe_shift_points"]
    )

    all_features = [col for col in df_full.columns if col not in excluded_features]

    # 2. Fonction d'objectif Optuna
    def objective(trial):
        # Sélection binaire des features
        selected_features = [
            f for f in all_features
            if trial.suggest_categorical(f"use_{f}", [True, False])
        ]

        if len(selected_features) == 0:
            raise optuna.TrialPruned()

        df_selected = df_full[selected_features]

        # Réduction par corrélation
        kept_features, _ = remove_highly_correlated_features(
            df_selected, features=selected_features, threshold=correlation_threshold
        )

        if len(kept_features) < 2:
            raise optuna.TrialPruned()

        # Entraînement et évaluation du modèle
        score = models.optimize_model_regression_simple_split(
            trial=trial,
            horizon=model,
            uid=uid,
            df=df_full,
            selected_features=kept_features,
            model_params=model_params,
            strict_pruning=True,
            baseline_score=baseline_score
        )

        # Pénalité douce selon nombre de features sélectionnées
        penalty = 0.01 * len(kept_features)
        adjusted_score = score + penalty

        return adjusted_score


    logger.send_log(f"ℹ️ - {base_log} - Début d'optimisation par ablation avec {n_trials} n_trials", "info")
    # 3. Lancer Optuna
    n_startup_trials = int(n_trials * 0.2)
    study = optuna.create_study(direction="maximize")
    study.set_user_attr("startup_trials", n_startup_trials)

    # 3.1 Enqueue un trial avec toutes les features à True (non bloquant)
    initial_params = {f"use_{f}": True for f in all_features}
    try:
        study.enqueue_trial(initial_params)
        logger.send_log(f"✅ - {base_log} - Trial initial (toutes features) enqueuée avec succès", "debug")
    except Exception as e:
        logger.send_log(f"⚠️ - {base_log} - Impossible d'enqueue le trial initial: {e}", "warning")

    study.optimize(objective, n_trials=n_trials)


    # 4. Résultats finaux
    best_trial = study.best_trial
    best_features_raw = [k.replace("use_", "") for k, v in best_trial.params.items() if v is True]

    # Supprimer les corrélées une dernière fois pour affichage propre
    df_best = df_full[best_features_raw]
    final_features, removed_correlated = remove_highly_correlated_features(
        df_best, features=best_features_raw, threshold=correlation_threshold
    )

    logger.send_log(f"🏆 - {base_log} - Meilleure combinaison via Optuna : {final_features}", "info")
    logger.send_log(f"ℹ️ - {base_log} - Combined score = {best_trial.value:.4f}", "info")

    try:
        study.trials.clear()
        study._storage = None
    except Exception:
        pass

    del df_full
    del study
    gc.collect()

    return {
        "useful": final_features,
        "params": best_trial.params,
        "correlated_removed": list(set(best_features_raw) - set(final_features))
    }



def prepare_df_individual_optim(group_name, candlesticks, indicator_params, uid, model, selected_features):
    # Vérification logique spécifique au groupe
    if group_name == "MACD":
        if indicator_params["MACD_fast"] >= indicator_params["MACD_slow"]:
            raise optuna.TrialPruned()

    df = None
    try:
        # Génère le DataFrame complet avec tous les indicateurs
        df = indicator.generate_training_sample(
            candlesticks, indicator_params, uid, model["shift_value"],
            model["horizon_hour"], "max", model['safe_shift_points']
        )

        # Sélectionne uniquement les features utiles
        features_to_keep = selected_features + ['log_return']
        df_reduced = models.keep_only_features(df, features_to_keep)

        # Libération mémoire de df complet
        del df
        gc.collect()

        return df_reduced

    except Exception as e:
        logger.send_log(f"❌ - {uid} - Erreur préparation features : {e}", "error")
        raise optuna.TrialPruned()



def optimize_grouped_indicators(uid, model, candlesticks, model_params, best_periods, kept_features):
    """
    Optimise les indicateurs techniques individuellement par groupe à l'aide d'Optuna.
    Ne garde que les features présentes dans kept_features (issues de l'ablation),
    et les paramètres associés contenant le nom de la feature.
    """
    base_log = f"{uid} - {model['name']}"

    for group_name, group_content in INDICATOR_GROUPED.items():
        raw_features = group_content["features"]
        raw_params = group_content["params"]

        # 🔍 Filtrage des features selon kept_features
        selected_features = [f for f in raw_features if f in kept_features]

        # 🔍 Filtrage des paramètres associés aux features conservées
        param_names = []
        for f in selected_features:
            param_names += [p for p in raw_params if f in p]

        # Suppression des doublons éventuels
        param_names = list(set(param_names))

        if not selected_features or not param_names:
            logger.send_log(f"⚠️ - {base_log} - {group_name} - Aucune feature/paramètre retenu après filtrage (skipping)", "warning")
            continue

        logger.send_log(f"ℹ️ - {base_log} - {group_name} - Features retenues : {selected_features}", "debug")
        logger.send_log(f"ℹ️ - {base_log} - {group_name} - Params retenus : {param_names}", "debug")

        n_trials = GROUPED_FEATURE_TRIAL * len(param_names)
        n_startup_trials = int(n_trials * 0.2)

        study = optuna.create_study(
            directions=["maximize"],
            study_name=f"opt_{group_name}",
            sampler=optuna.samplers.TPESampler(n_startup_trials=n_startup_trials, multivariate=True)
        )
        study.set_user_attr("startup_trials", n_startup_trials)

        def objective(trial):
            df = prepare_df_individual_optim(
                trial,
                candlesticks,
                suggest_global_params(trial, best_periods),
                uid,
                model,
                selected_features
            )

            result = models.optimize_model_regression_simple_split(
                trial=trial,
                horizon=model,
                uid=uid,
                selected_features=selected_features,  # features globales gardées
                df=df,
                model_params=model_params,
                strict_pruning=True
            )

            del df
            gc.collect()
            return result

        # Réinjection des meilleurs params
        if best_periods:
            previous_params = best_periods.get("indicator_params", {})
            group_params = {
                k: v for k, v in previous_params.items() if k in param_names
            }
            if len(group_params) == len(param_names):
                try:
                    study.enqueue_trial(group_params)
                    logger.send_log(f"✅ - {base_log} - {group_name} - Paramètres précédents enqueuées", "debug")
                except Exception as e:
                    logger.send_log(f"⚠️ - {base_log} - {group_name} - Impossible d'enqueue les paramètres précédents: {e}", "warning")

        logger.send_log(f"ℹ️ - {base_log} - {group_name} - Début d'optimisation groupée avec {n_trials} trials", "info")
        study.optimize(objective, n_trials=n_trials, n_jobs=1)

        if not study.best_trials:
            logger.send_log(f"❌ - {base_log} - {group_name} - Aucun trial n'a été validé", "error")
            continue

        best_trial = sorted(study.best_trials, key=lambda t: t.values[0])[0]
        best_params_group = best_trial.user_attrs.get("indicator_params", {})

        for k in param_names:
            if k in best_params_group:
                old_val = best_periods.get(k, None)
                logger.send_log(f"ℹ️ - {base_log} - {group_name} - paramètre {k} mis à jour avec {best_params_group[k]}. Valeur initiale = {old_val}.", "info")
                best_periods[k] = best_params_group[k]

        evaluation = best_trial.user_attrs.get("evaluation", {})
        logger.send_log(f"ℹ️ - {base_log} - {group_name} - Evaluation = {evaluation}", "info")

        # Cleanup
        del best_trial, best_params_group, evaluation
        try:
            study.trials.clear()
            study._storage = None
        except Exception:
            pass
        del study
        gc.collect()

    return best_periods




def optimize_individual_indicators(uid, model, candlesticks, model_params, baseline_score):
    """
    Optimise les indicateurs techniques individuellement par groupe à l'aide d'Optuna.

    Returns:
        dict: Meilleurs paramètres individuels trouvés
    """
    base_log = f"{uid} - {model['name']}"

    logger.send_log(f"ℹ️ - {base_log} - candlesticks = {len(candlesticks)}", "info")

    try:
        previous_best_indicator_params_raw = model_params['indicator_params']
        # Fusion intelligente : anciens paramètres + nouveaux indicateurs avec valeurs par défaut
        best_periods = merge_indicator_params(
            previous_best_indicator_params_raw.get('indicator_params', previous_best_indicator_params_raw),
            model['horizon_hour']
        )
    except Exception as e:
        logger.send_log(f"⚠️ - {base_log} - Erreur lors de la récupération des meilleurs paramètres d'indicateur : {e}","error")
        return None

    for group_name, param_names in INDICATOR_GROUPS.items():
        selected_features = param_names["features"]
        param_names = param_names["params"]
        n_trials = INDIVIDUAL_FEATURE_TRIAL * (len(param_names))
        n_startup_trials = int(n_trials * 0.2)

        study = optuna.create_study(
            directions=["minimize"],
            study_name=f"opt_{group_name}",
            sampler=optuna.samplers.TPESampler(n_startup_trials=n_startup_trials, multivariate=True)
        )
        study.set_user_attr("startup_trials", n_startup_trials)

        def objective(trial):
            result = None
            df = None
            try:
                # Utiliser les paramètres par défaut comme base
                indicator_params = get_full_default_params(model['horizon_hour'])

                # Optimiser seulement les paramètres du groupe actuel
                for param in param_names:
                    cfg = INDICATOR_CONFIG_LARGE[param]
                    low = int(model['horizon_hour'] * cfg["min_factor"])
                    high = int(model['horizon_hour'] * cfg["max_factor"])
                    indicator_params[param] = trial.suggest_int(param, low, high)

                # Contraintes logiques pour MACD
                if "MACD_fast" in indicator_params and "MACD_slow" in indicator_params:
                    if indicator_params["MACD_fast"] >= indicator_params["MACD_slow"]:
                        indicator_params["MACD_fast"] = max(2, indicator_params["MACD_slow"] - 1)

                trial.set_user_attr("indicator_params", indicator_params)

                df = prepare_df_individual_optim(
                    group_name=group_name,
                    candlesticks=candlesticks,
                    indicator_params=indicator_params,
                    uid=uid,
                    model=model,
                    selected_features=selected_features
                )

                if df is None or df.empty:
                    logger.send_log(f"❌ - {uid} - {group_name} - DataFrame vide pour : {indicator_params}", "error")
                    raise optuna.TrialPruned()

                result = models.optimize_model_regression_simple_split(
                    trial=trial,
                    horizon=model,
                    uid=uid,
                    df=df,
                    model_params=model_params,
                    strict_pruning=True,
                    baseline_score=baseline_score
                )

                return result

            except optuna.TrialPruned:
                raise

            except Exception as e:
                import traceback
                tb = traceback.format_exc()
                logger.send_log(f"❌ - {uid} - {group_name} - Erreur dans le trial : {e}\n{tb}", "error")
                raise optuna.TrialPruned()

            finally:
                try:
                    del df
                except:
                    pass
                try:
                    del result
                except:
                    pass
                try:
                    trial.user_attrs.clear()
                    if hasattr(trial, "intermediate_values"):
                        trial.intermediate_values.clear()
                except:
                    pass

                #log_memory_snapshot(uid, context=f"{model['name']} - {group_name} - trial")

                gc.collect()




        # ➕ Réinjection des meilleurs paramètres précédents (si complets)
        if previous_best_indicator_params_raw:
            previous_params = previous_best_indicator_params_raw.get("indicator_params", previous_best_indicator_params_raw)
            group_params = {
                k: v for k, v in previous_params.items() if k in param_names
            }
            if len(group_params) == len(param_names):
                try:
                    study.enqueue_trial(group_params)
                    logger.send_log(f"✅ - {base_log} - Paramètres précédents enqueuées pour optimisation individuelle", "debug")
                except Exception as e:
                    logger.send_log(f"⚠️ - {base_log} - Impossible d'enqueue les paramètres précédents: {e}", "warning")

        # 🔁 Lancement de l’optimisation
        logger.send_log(f"ℹ️ - {base_log} - {group_name} Début d'optimisation individuelle avec {n_trials} n_trials", "info")

        try:
            study.optimize(objective, n_trials=n_trials, n_jobs=1)
        except Exception as e:
            logger.send_log(f"⚠️ - {base_log} - {group_name} - Erreur  lors de l'optimisation' : {e}","error")
            return None

        if not study.best_trials:
            logger.send_log(f"❌ - {base_log} - {group_name} - Aucun trial n'a été validé", "error")
            continue

        best_trial = sorted(study.best_trials, key=lambda t: t.values[0])[0]
        best_params_group = best_trial.user_attrs.get("indicator_params", {})

        logger.send_log(f"ℹ️ - {base_log} - {group_name} - {best_trial} - {best_params_group}", "info")

        for k in param_names:
            if k in best_params_group:
                logger.send_log(f"ℹ️ - {base_log} - {group_name} - paramètre {k} mise à jour avec {best_params_group[k]}. Valeur initiale = {best_periods[k]}.", "info")
                best_periods[k] = best_params_group[k]

        evaluation = best_trial.user_attrs.get("evaluation", {})
        logger.send_log(f"ℹ️ - {base_log} - {group_name} - Evaluation = {evaluation}", "info")



        # Nettoyage mémoire
        del best_trial
        del best_params_group
        del evaluation

        try:
            study.trials.clear()
            study._storage = None
        except Exception:
            pass

        del study
        gc.collect()


    return best_periods



def baseline_training(model, uid, candlesticks, indicator_params, model_params, selected_features=None):
    start_df = time.time()
    df = indicator.generate_training_sample(
        candlesticks, indicator_params, uid,
        model["shift_value"], model["horizon_hour"], "max", model['safe_shift_points']
    )

    diagnose_log_return(df, uid, model['horizon_hour'])

    duration_df = time.time() - start_df

    try:
        start_1 = time.time()
        baseline_score = models.optimize_model_regression_simple_split(
            trial=DummyTrial(),
            horizon=model,
            uid=uid,
            df=df,
            selected_features=selected_features,
            model_params=model_params,
            strict_pruning="off"
        )
        duration_1 = time.time() - start_1

        start_2 = time.time()
        double_check = models.optimize_model_regression_simple_split(
            trial=DummyTrial(),
            horizon=model,
            uid=uid,
            df=df,
            selected_features=selected_features,
            model_params=model_params,
            strict_pruning="off"
        )
        duration_2 = time.time() - start_2

        estimated_op = HARSH_PARAMETER_OPTIM + SHARP_PARAMETERS_OPTIM + ABLATION_TRIALS + (41 * INDIVIDUAL_FEATURE_TRIAL) + (35 * GROUPED_FEATURE_TRIAL)
        estimated_time = int(((((duration_1 + duration_2) / 2) * estimated_op) / 60) / 60)
        logger.send_log(f"⏱️ - Temps estimé pour la complétion de l'entrainement : {estimated_time}h. Temps pour 1 entrainement : {duration_1}. Temps préparation df = {duration_df}", "info")

        if double_check != baseline_score:
            logger.send_log("⚠️ - Modèle non déterministe ! Les résultats peuvent être biaisés", "error")

    except Exception as e:
        logger.send_log(f"⚠️ - Erreur lors de la génération du baseline_score : {e}", "error")
        return None

    del df

    return baseline_score



def verify_optimisation(model, uid, candlesticks, indicator_params, model_params, baseline_score, selected_features=None):
    try:
        base_log = f"{uid} - {model['name']}"

        df = indicator.generate_training_sample(
            candlesticks, indicator_params, uid, model["shift_value"],
            model["horizon_hour"], "max", model['safe_shift_points']
        )

        if selected_features is not None:
            features_to_keep = selected_features + ['log_return']  # Création d'une nouvelle liste
            df = models.keep_only_features(df, features_to_keep)

        final_score = models.optimize_model_regression_simple_split(
            trial=DummyTrial(),
            horizon=model,
            uid=uid,
            df=df,
            selected_features=selected_features,
            model_params=model_params,
            strict_pruning="off"
        )

        delta_score = final_score - baseline_score
        if delta_score > 0:
            logger.send_log(f"✅ - {base_log} - Etape d'optimisation bénéfique au modèle ({delta_score})", "info")
        elif delta_score < 0:
            logger.send_log(f"❌ - {base_log} - Etape d'optimisation néfaste au modèle ({delta_score})", "warning")
        else:
            logger.send_log(f"⚠️ - {base_log} - Etape d'optimisation sans effet", "info")

        del df
    except Exception as e:
        logger.send_log(f"⚠️ - {base_log} - Erreur lors de la génération du score de vérification : {e}","error")
        return baseline_score
    return final_score






def process_crypto(uid, model):
    base_log = f"{uid} - {model['name']}"
    until_date = datetime.utcnow() #- timedelta(minutes=minutes_depth)

    # ==========================================================================================
    # 0. Récupère les données nécessaires
    # ==========================================================================================
    # Get candlesticks
    try :
        candlesticks = mongo.get_candlesticks(uid, until_date)
        if len(candlesticks) == 0:
            logger.send_log(f"⚠️ - {base_log} - Aucun candlesticks trouvés","error")
            return None
    except Exception as e:
        logger.send_log(f"⚠️ - {base_log} - Erreur lors de get_candelsticks : {e}","error")
        return None

    # Get best hyperparams
    try :
        model_params = mongo.get_best_hyperparams(model["name"], uid, model["horizon_hour"])
        #if model_params['created_at'] > datetime.utcnow() - timedelta(days=5):
        #    logger.send_log(f"⚠️ - {base_log} - Paramètres déjà optimisés récemment. Sortie anticipée","warning")
        #    return None
        selected_features = model_params.get('features.useful', None)
    except Exception as e:
        logger.send_log(f"⚠️ - {base_log} - Erreur lors de la récupération des hyperparamètres : {e}","error")
        return None



    # ==========================================================================================
    # 0. Création du baseline score de référence
    # ==========================================================================================
    previous_best_indicator_params_raw = model_params['indicator_params']
    # Fusion intelligente : anciens paramètres + nouveaux indicateurs avec valeurs par défaut
    previous_best_indicator_params = merge_indicator_params(
        previous_best_indicator_params_raw.get('indicator_params', previous_best_indicator_params_raw),
        model['horizon_hour']
    )
    #debug_training(model, uid, candlesticks, previous_best_indicator_params, model_params['best_params'])
    baseline_score = baseline_training(model, uid, candlesticks, previous_best_indicator_params, model_params['best_params'])
    logger.send_log(f"ℹ️ - {base_log} - Baseline Score = {baseline_score}","info")


    # ==========================================================================================
    # 1. Optimisation des paramètres du modèle avec les paramètres d'indicateurs existants
    # ==========================================================================================
    try:
        df = indicator.generate_training_sample(
            candlesticks, previous_best_indicator_params, uid, model["shift_value"],
            model["horizon_hour"], "max", model['safe_shift_points']
        )
        # Calcul adaptatif du nombre de trials
        adaptive_harsh_trials = get_adaptive_trials(model['horizon_hour'], "harsh", len(df))
        best_params = optimize_model_parameters_enhanced(uid, model, df, baseline_score, model_params['best_params'], n_trials=adaptive_harsh_trials)
        del df

    except Exception as e:
        logger.send_log(f"⚠️ - {base_log} - Erreur lors de l'optimisation des paramètres du modèle : {e}","error")
        return None

    optimized_parameters_score = verify_optimisation(model, uid, candlesticks, previous_best_indicator_params, best_params, baseline_score)


    # ==========================================================================================
    # 2. Optimisation des paramètres d'indicateurs par groupe isolés
    # ==========================================================================================
    try:
        best_periods = optimize_individual_indicators(uid, model, candlesticks, best_params, optimized_parameters_score)
    except Exception as e:
        logger.send_log(f"⚠️ - {base_log} - Erreur lors de l'optimisation des indicateurs individuels : {e}","error")
        return None

    logger.send_log(f"ℹ️ - {base_log} - best periods : {best_periods}","info")
    isolated_indicator_score = verify_optimisation(model, uid, candlesticks, best_periods, best_params, optimized_parameters_score)


     # ==========================================================================================
    # 3. Analyse par ablation
    # ==========================================================================================
    try:
        # Calcul adaptatif du nombre de trials pour l'ablation
        adaptive_ablation_trials = get_adaptive_trials(model['horizon_hour'], "ablation")
        feature_impacts = feature_ablation_analysis(uid, model, candlesticks, best_params, best_periods, correlation_threshold=0.95, baseline_score=isolated_indicator_score, n_trials=adaptive_ablation_trials)

        if len(feature_impacts['useful']) == 0:
            logger.send_log(f"⚠️ - {base_log} - Aucune feature gardée lors de l'ablation","error")
            return None
        logger.send_log(f"ℹ️ - {base_log} - Résultats ablation : {feature_impacts}", "info")
    except Exception as e:
        logger.send_log(f"⚠️ - {base_log} - Erreur dans l’ablation : {e}", "error")

    ablation_indicator_score = verify_optimisation(model, uid, candlesticks, best_periods, best_params, isolated_indicator_score, selected_features=feature_impacts['useful'])


    # ==========================================================================================
    # 4. Optimisation fine des paramètres du modèle avec les paramètres d'indicateurs existants
    # ==========================================================================================
    try:
        df = indicator.generate_training_sample(
            candlesticks, best_periods, uid, model["shift_value"],
            model["horizon_hour"], "max", model['safe_shift_points']
        )
        features_to_keep = feature_impacts['useful'] + ['log_return']
        df = models.keep_only_features(df, features_to_keep)
        # Calcul adaptatif du nombre de trials pour l'optimisation fine
        adaptive_sharp_trials = get_adaptive_trials(model['horizon_hour'], "sharp", len(df))
        best_params_fine = optimize_model_parameters_enhanced(uid, model, df, baseline_score, best_params, n_trials=adaptive_sharp_trials)
        logger.send_log(f"ℹ️ - {base_log} - best params : {best_params_fine}","info")
        del df

    except Exception as e:
        logger.send_log(f"⚠️ - {base_log} - Erreur lors de l'optimisation fine des paramètres du modèle : {e}","error")
        return None

    try:
        final_optimized_parameters_score = verify_optimisation(model, uid, candlesticks, best_periods, best_params_fine, ablation_indicator_score, selected_features=feature_impacts['useful'])
    except Exception as e:
        logger.send_log(f"⚠️ - {base_log} - Erreur lors la vérif du modèle : {e}","error")
        return None

    # ==========================================================================================
    # Finalisation
    # ==========================================================================================
    delta_score = final_optimized_parameters_score - baseline_score
    if delta_score > 0:
        logger.send_log(f"✅ - {base_log} - Optimisation réussie, amélioration = ({delta_score})", "info")
    else:
        logger.send_log(f"❌ - {base_log} - Optimisation ratée = ({delta_score})", "error")



    data_hyperparams = {
        "uid": uid,
        "horizon": model['name'],
        "best_params": best_params_fine,
        "indicator_params": best_periods,
        "feature_impacts": feature_impacts,
        "final_score": final_optimized_parameters_score
    }
    mongo.create_hyperparams(model["name"], uid, data_hyperparams)
    logger.send_raw_data_log(data_hyperparams, metric="models_params")

    gc.collect()
    return True



def init_worker():
    global logger, mongo, models, indicator
    logger = GrafanaUtils(service=SERVICE)
    mongo = MongoUtils()
    mongo.connect(service=SERVICE)
    models = ModelsUtils(service=SERVICE)
    indicator = IndicatorsUtils(service=SERVICE)



def worker(uid, horizon):
    init_worker()

    base_log = f"{uid} - {horizon}"
    try:
        process_crypto(uid, horizon)
        logger.send_log(f"✅ - {base_log} terminé", "info")
        return f"✅ - {base_log} terminé"
    except Exception as e:
        logger.send_log(f"❌ - {base_log} erreur : {e}", "error")
        return f"❌ - {base_log} erreur : {e}"



def optimize_features_for_all(trading_uids, horizons, max_workers=1):
    logger.send_log("✅ Lancement du calcul des indicateurs", "info")
    logger.send_log(f"📌 Début du calcul des indicateurs pour {len(trading_uids)} cryptos et {len(horizons)} horizons...", "info")


    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        futures = [executor.submit(worker, uid, horizon) for uid in trading_uids for horizon in horizons]

        for future in as_completed(futures):
            msg = future.result()
            logger.send_log(msg, "info" if "✅" in msg else "error")

    logger.send_log("🏁 Fin du calcul des indicateurs pour toutes les cryptos.", "info")
    return True



def run_sequential_optimizations():
    """
    Exécute l'optimisation des paramètres des indicateurs pour chaque modèles pour chaque crypto.
    """
    trading_uids = mongo.get_trading_pairs()

    if not trading_uids:
        logger.send_log("⚠️ Aucune crypto à entraîner (test ou trading) !","warning")
        return False

    horizons = mongo.get_all_horizons()

    if len(horizons) == 0:
        logger.send_log("⚠️ Aucun horizon trouvé en db !","warning")
        return False

    # 🔥 Exécuter les optimisations en parallèle avec ThreadPoolExecutor
    try:
        optimize_features_for_all(trading_uids, horizons, max_workers=2)
    except Exception as e:
        logger.send_log(f"❌ - Erreur lors de l'optimisation des indicateurs : {e}", "error")


    logger.send_log("✅ Toutes les optimisations d'indicateurs sont terminées !","info")
    return True



def main():
    INTERVAL = 5
    error_count = 0

    while True:
        try:
            logger.send_log("🚀 Lancement d'un nouveau cycle d'optimisation des paramètres d'indicateurs...", "info")
            success = run_sequential_optimizations()

            if success:
                logger.send_log(f"✅ Cycle terminé. Prochain dans {INTERVAL} sec...", "info")
                error_count = 0
            else:
                logger.send_log("⚠️ Aucune optimisation réalisée.", "warning")

        except Exception as e:
            error_count += 1
            logger.send_log(f"❌ Erreur dans `main()` : {e} (tentative {error_count})", "error")
            if error_count >= 5:
                logger.send_log("🚨 Trop d'erreurs, arrêt forcé.", "critical")
                break

        time.sleep(60)

    logger.send_log("👋 Arrêt du programme terminé proprement.", "info")

if __name__ == "__main__":
    main()
